﻿using App.Base.Repository;
using App.Base.Utilities;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units;

using AutoMapper;

using MongoDB.Bson;
using MongoDB.Driver;

namespace App.ECommerce.Repository.Implement;

public class VoucherRepository : BaseRepository, IVoucherRepository
{
    private readonly IMongoCollection<Voucher> _collectionVoucher;
    private readonly IMongoCollection<VoucherHistory> _collectionVoucherHistory;
    private readonly IMongoCollection<VoucherUser> _collectionVoucherUser;
    private readonly IMongoCollection<VoucherDetail> _collectionVoucherDetail;
    private readonly IMongoCollection<VoucherBranch> _collectionVoucherBranch;
    private readonly IUserRepository _userRepository;
    private readonly IItemsRepository _itemsRepository;
    private readonly IMapper _mapper;

    public VoucherRepository(
        IMapper mapper,
        IUserRepository userRepository,
        IItemsRepository itemsRepository) : base()
    {
        _collectionVoucher = _database.GetCollection<Voucher>($"Voucher");
        _collectionVoucherHistory = _database.GetCollection<VoucherHistory>($"VoucherHistory");
        _collectionVoucherUser = _database.GetCollection<VoucherUser>($"VoucherUser");
        _collectionVoucherDetail = _database.GetCollection<VoucherDetail>($"VoucherDetail");
        _collectionVoucherBranch = _database.GetCollection<VoucherBranch>($"VoucherBranch");
        _itemsRepository = itemsRepository;
        _userRepository = userRepository;
        _mapper = mapper;

        var indexOptions = new CreateIndexOptions();
        var indexModelVoucher = new List<CreateIndexModel<Voucher>>()
        {
            new CreateIndexModel<Voucher>(Builders<Voucher>.IndexKeys.Ascending(item => item.VoucherId), indexOptions),
            new CreateIndexModel<Voucher>(Builders<Voucher>.IndexKeys.Ascending(item => item.VoucherType), indexOptions),
            new CreateIndexModel<Voucher>(Builders<Voucher>.IndexKeys.Ascending(item => item.VoucherName), indexOptions),
        };
        _collectionVoucher.Indexes.CreateMany(indexModelVoucher);
    }

    //=== Voucher
    #region Voucher

    private async Task<List<Items>> GetRewardProducts(List<string> rewardGiftIds)
    {
        if (rewardGiftIds == null || !rewardGiftIds.Any())
            return new List<Items>();

        return _itemsRepository.FindByItemsIds(string.Join(",", rewardGiftIds));
    }

    public async Task<Voucher> CreateVoucher(Voucher item)
    {
        item.Id = Guid.NewGuid();
        item.VoucherId = Guid.NewGuid().ToString();
        item.VoucherNameOrigin = item.VoucherName.NonUnicode().ToLower();
        await _collectionVoucher.InsertOneAsync(item);
        return item;
    }

    public async Task<Voucher> RestoreVoucher(Voucher item)
    {
        item.Id = Guid.NewGuid();
        item.VoucherId = !string.IsNullOrEmpty(item.VoucherId) ? item.VoucherId : Guid.NewGuid().ToString();
        item.VoucherNameOrigin = item.VoucherName.NonUnicode().ToLower();
        await _collectionVoucher.InsertOneAsync(item);
        return item;
    }

    public async Task<Voucher> DeleteVoucher(string voucherId)
    {
        var filter = Builders<Voucher>.Filter.Eq(x => x.VoucherId, voucherId);
        var update = Builders<Voucher>.Update
            .Set(x => x.IsDeleted, true);
        var options = new FindOneAndUpdateOptions<Voucher> { ReturnDocument = ReturnDocument.After };
        return await _collectionVoucher.FindOneAndUpdateAsync(filter, update, options);
    }

    public async Task<bool> DeleteVouchers(List<string> voucherIds)
    {
        var filter = Builders<Voucher>.Filter.In(x => x.VoucherId, voucherIds);
        var update = Builders<Voucher>.Update
            .Set(x => x.IsDeleted, true);
        var result = await _collectionVoucher.UpdateManyAsync(filter, update);

        // Cập nhật isDeleted cho VoucherDetail liên quan
        var filterDetail = Builders<VoucherDetail>.Filter.In(x => x.VoucherId, voucherIds);
        var updateDetail = Builders<VoucherDetail>.Update.Set(x => x.IsDeleted, true);
        await _collectionVoucherDetail.UpdateManyAsync(filterDetail, updateDetail);

        // Cập nhật isDeleted cho VoucherUser liên quan
        var filterUser = Builders<VoucherUser>.Filter.In(x => x.VoucherId, voucherIds);
        var updateUser = Builders<VoucherUser>.Update.Set(x => x.IsDeleted, true);
        await _collectionVoucherUser.UpdateManyAsync(filterUser, updateUser);

        return result.ModifiedCount > 0;
    }

    public async Task<Voucher?> FindByVoucherId(string voucherId)
    {
        var filter = Builders<Voucher>.Filter.And(
            Builders<Voucher>.Filter.Eq(item => item.VoucherId, voucherId),
            Builders<Voucher>.Filter.Eq(item => item.IsDeleted, false)
        );
        var voucher = await _collectionVoucher.Find(filter).FirstOrDefaultAsync();
        if (voucher != null)
        {
            var voucherDetails = await _collectionVoucherDetail
                .Find(x => x.VoucherId == voucher.VoucherId && !x.IsDeleted)
                .ToListAsync();
            voucher.VoucherDetails = voucherDetails;

            // Get reward products if RewardType is "product"
            if (voucher.RewardType == TypeReward.Product && voucher.RewardGiftIds != null && voucher.RewardGiftIds.Any())
            {
                var rewardProducts = await GetRewardProducts(voucher.RewardGiftIds);
                voucher.RewardProducts = rewardProducts;
            }

            // Tính toán RemainingStock
            var distributedCount = await GetVoucherDistributedCount(voucher.VoucherId);
            voucher.RemainingStock = Math.Max(0, voucher.Quantity - distributedCount);
        }
        return voucher;
    }

    public async Task<Voucher?> FindByVoucherIdAndShopId(string voucherId, string shopId)
    {
        var filter = Builders<Voucher>.Filter.And(
            Builders<Voucher>.Filter.Eq(item => item.VoucherId, voucherId),
            Builders<Voucher>.Filter.Eq(item => item.ShopId, shopId),
            Builders<Voucher>.Filter.Eq(item => item.IsDeleted, false)
        );
        return await _collectionVoucher.Find(filter).FirstOrDefaultAsync();
    }

    public async Task<List<Voucher>> FindByVoucherIds(string voucherIds)
    {
        var listIds = voucherIds.Split(',');
        var filter = Builders<Voucher>.Filter.And(
            Builders<Voucher>.Filter.In(x => x.VoucherId, listIds),
            Builders<Voucher>.Filter.Eq(x => x.IsDeleted, false)
        );
        return await _collectionVoucher.Find(filter).ToListAsync();
    }

    public async Task<PagingResult<Voucher>> ListVoucher(FilterVoucherDto filterVoucher)
    {
        var result = new PagingResult<Voucher>();
        var filters = new List<FilterDefinition<Voucher>>();

        filters.Add(Builders<Voucher>.Filter.Eq(x => x.IsDeleted, false));

        if (!string.IsNullOrEmpty(filterVoucher.ShopId))
        {
            filters.Add(Builders<Voucher>.Filter.Eq(p => p.ShopId, filterVoucher.ShopId));
        }

        if (filterVoucher.StatusVoucher != null)
        {
            filters.Add(Builders<Voucher>.Filter.Eq(p => p.Status, filterVoucher.StatusVoucher));
        }

        if (filterVoucher?.Type != null && filterVoucher.Type.Any())
        {
            filters.Add(Builders<Voucher>.Filter.In(p => p.VoucherType, filterVoucher.Type));
        }

        if (filterVoucher?.CodeType != null)
        {
            filters.Add(Builders<Voucher>.Filter.Eq(p => p.CodeType, filterVoucher.CodeType));
        }

        if (filterVoucher.DistributionType != null)
        {
            filters.Add(Builders<Voucher>.Filter.Eq(p => p.DistributionType, filterVoucher.DistributionType));
        }

        List<string> matchingVoucherIds = null;
        if (!string.IsNullOrEmpty(filterVoucher.Paging.Search))
        {
            var search = filterVoucher.Paging.Search.NonUnicode().ToLower().EscapeSpecialChars();
            var voucherDetailFilter = Builders<VoucherDetail>.Filter.And(
                Builders<VoucherDetail>.Filter.Regex(x => x.VoucherCode, new BsonRegularExpression(search, "i")),
                Builders<VoucherDetail>.Filter.Eq(x => x.IsDeleted, false)
            );
            matchingVoucherIds = await _collectionVoucherDetail
                .Find(voucherDetailFilter)
                .Project(x => x.VoucherId)
                .ToListAsync();

            if (matchingVoucherIds.Any())
            {
                filters.Add(Builders<Voucher>.Filter.In(x => x.VoucherId, matchingVoucherIds));
            }
            else
            {
                result.Total = 0;
                result.Result = new List<Voucher>();
                return result;
            }
        }

        var filter = filters.Any() ? Builders<Voucher>.Filter.And(filters) : Builders<Voucher>.Filter.Empty;
        var query = _collectionVoucher.Find(filter);
        result.Total = await query.CountDocumentsAsync();
        var sortDefinition = Builders<Voucher>.Sort.Descending(x => x.CreatedDate);
        var vouchers = await query
            .Sort(sortDefinition)
            .Skip(filterVoucher.Paging.PageIndex * filterVoucher.Paging.PageSize)
            .Limit(filterVoucher.Paging.PageSize)
            .ToListAsync();

        foreach (var voucher in vouchers)
        {
            if (voucher.CodeType == TypeVoucherCode.Common)
            {
                var voucherDetails = await _collectionVoucherDetail
                    .Find(x => x.VoucherId == voucher.VoucherId && !x.IsDeleted)
                    .ToListAsync();
                voucher.VoucherDetails = voucherDetails;
                voucher.VoucherDetailCount = voucherDetails.Count;
            }
            else // Unique
            {
                var detailCount = await _collectionVoucherDetail.CountDocumentsAsync(x => x.VoucherId == voucher.VoucherId && !x.IsDeleted);
                voucher.VoucherDetailCount = (int)detailCount;
                voucher.VoucherDetails = null;
            }

            // Tính toán RemainingStock: Quantity - số voucher đã phát cho user
            var distributedCount = await GetVoucherDistributedCount(voucher.VoucherId);
            voucher.RemainingStock = Math.Max(0, voucher.Quantity - distributedCount);
        }

        result.Result = vouchers;
        return result;
    }

    public async Task<PagingResult<Voucher>> ListVoucherByUserId(Paging paging, string userId, string? shopId = null)
    {
        PagingResult<Voucher> result = new PagingResult<Voucher>();

        // Build base filter for voucher users
        var baseFilters = new List<FilterDefinition<VoucherUser>>
        {
            Builders<VoucherUser>.Filter.Eq(vu => vu.UserId, userId),
            Builders<VoucherUser>.Filter.Eq(vu => vu.IsDeleted, false)
        };

        // Add search filter if provided
        if (!string.IsNullOrEmpty(paging.Search))
        {
            // Search in voucher details by code
            var voucherDetailFilter = Builders<VoucherDetail>.Filter.And(
                Builders<VoucherDetail>.Filter.Regex(x => x.VoucherCode, new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "").EscapeSpecialChars()}", "i")),
                Builders<VoucherDetail>.Filter.Eq(x => x.IsDeleted, false)
            );
            var matchingVoucherDetailIds = await _collectionVoucherDetail
                .Find(voucherDetailFilter)
                .Project(x => x.VoucherDetailId)
                .ToListAsync();

            if (matchingVoucherDetailIds.Any())
            {
                baseFilters.Add(Builders<VoucherUser>.Filter.In(x => x.VoucherDetailId, matchingVoucherDetailIds));
            }
            else
            {
                result.Total = 0;
                result.Result = new List<Voucher>();
                return result;
            }
        }

        var finalFilter = Builders<VoucherUser>.Filter.And(baseFilters);
        var query = _collectionVoucherUser.Find(finalFilter);

        // Get all voucher users (không phân trang để lọc)
        var sortDefinition = Builders<VoucherUser>.Sort.Descending(x => x.CreatedDate);
        var allVoucherUsers = await query.Sort(sortDefinition).ToListAsync();

        if (!allVoucherUsers.Any())
        {
            result.Result = new List<Voucher>();
            result.Total = 0;
            return result;
        }

        // Get all voucherIds and detailIds
        var voucherIds = allVoucherUsers.Select(vu => vu.VoucherId).Distinct().ToList();
        var voucherDetailIds = allVoucherUsers.Select(vu => vu.VoucherDetailId).Distinct().ToList();

        // Build filter for vouchers
        var voucherFilter = Builders<Voucher>.Filter.And(
            Builders<Voucher>.Filter.In(x => x.VoucherId, voucherIds),
            Builders<Voucher>.Filter.Eq(x => x.IsDeleted, false),
            Builders<Voucher>.Filter.Eq(x => x.Status, TypeStatus.Actived),
            shopId == null ? Builders<Voucher>.Filter.Empty : Builders<Voucher>.Filter.Eq(x => x.ShopId, shopId)
        );

        // Get vouchers
        var vouchers = await _collectionVoucher.Find(voucherFilter).ToListAsync();

        // Get all voucher details
        var allVoucherDetails = await _collectionVoucherDetail
            .Find(x => voucherDetailIds.Contains(x.VoucherDetailId) && !x.IsDeleted)
            .ToListAsync();

        // Xây dựng danh sách voucher và lọc voucher hợp lệ
        var allValidVouchers = new List<Voucher>();
        foreach (var voucherUser in allVoucherUsers)
        {
            var voucher = vouchers.FirstOrDefault(v => v.VoucherId == voucherUser.VoucherId);
            if (voucher == null) continue;

            // Kiểm tra điều kiện hợp lệ
            var isValidVoucher = voucher.Status == TypeStatus.Actived &&
                                voucher.Quantity > voucher.QuantityUsed &&
                                (voucher.IsLongTerm || (voucher.EndDate.HasValue && DateTime.Now <= voucher.EndDate));
            if (!isValidVoucher) continue;

            // Tính toán RemainingStock cho voucher
            var distributedCount = await GetVoucherDistributedCount(voucher.VoucherId);
            voucher.RemainingStock = Math.Max(0, voucher.Quantity - distributedCount);

            if (voucher.CodeType == TypeVoucherCode.Common)
            {
                var detail = allVoucherDetails.FirstOrDefault(d => d.VoucherDetailId == voucherUser.VoucherDetailId);
                if (detail != null && voucherUser.NumUse > 0)
                {
                    detail.User = _userRepository.FindByUserId(voucherUser.UserId);
                    detail.NumUse = voucherUser.NumUse;
                    detail.BranchId = voucherUser.BranchId;
                    detail.CreatedDate = voucherUser.CreatedDate;
                    voucher.VoucherDetails = new List<VoucherDetail> { detail };
                    allValidVouchers.Add(voucher);
                }
            }
            else // Unique
            {
                var detail = allVoucherDetails.FirstOrDefault(d => d.VoucherDetailId == voucherUser.VoucherDetailId);
                if (detail != null && voucherUser.NumUse > 0)
                {
                    detail.User = _userRepository.FindByUserId(voucherUser.UserId);
                    detail.NumUse = voucherUser.NumUse;
                    detail.BranchId = voucherUser.BranchId;
                    detail.CreatedDate = voucherUser.CreatedDate;

                    var voucherClone = voucher.ShallowCopy();
                    voucherClone.RemainingStock = voucher.RemainingStock;
                    voucherClone.VoucherDetails = new List<VoucherDetail> { detail };
                    allValidVouchers.Add(voucherClone);
                }
            }
        }

        // Áp dụng phân trang cho danh sách hợp lệ
        var pagedValidVouchers = allValidVouchers
            .Skip(paging.PageIndex * paging.PageSize)
            .Take(paging.PageSize)
            .ToList();
        result.Result = pagedValidVouchers;
        result.Total = allValidVouchers.Count; // Tổng số voucher hợp lệ không phân trang
        return result;
    }

    public async Task<PagingResult<Voucher>> ListValidVouchersForCart(Paging paging, string userId, CartDto cartDto)
    {
        PagingResult<Voucher> result = new PagingResult<Voucher>();

        // Build base filter for voucher users
        var baseFilters = new List<FilterDefinition<VoucherUser>>
        {
            Builders<VoucherUser>.Filter.Eq(vu => vu.UserId, userId),
            Builders<VoucherUser>.Filter.Eq(vu => vu.IsDeleted, false)
        };

        var finalFilter = Builders<VoucherUser>.Filter.And(baseFilters);
        var query = _collectionVoucherUser.Find(finalFilter);

        // Get all voucher users (không phân trang)
        var sortDefinition = Builders<VoucherUser>.Sort.Descending(x => x.CreatedDate);
        var allVoucherUsers = await query.Sort(sortDefinition).ToListAsync();

        if (!allVoucherUsers.Any())
        {
            result.Result = new List<Voucher>();
            result.Total = 0;
            return result;
        }

        // Get all voucherIds and detailIds
        var voucherIds = allVoucherUsers.Select(vu => vu.VoucherId).Distinct().ToList();
        var voucherDetailIds = allVoucherUsers.Select(vu => vu.VoucherDetailId).Distinct().ToList();

        // Build filter for vouchers - bao gồm cả Promotion và Transport
        var voucherFilter = Builders<Voucher>.Filter.And(
            Builders<Voucher>.Filter.In(x => x.VoucherId, voucherIds),
            Builders<Voucher>.Filter.Eq(x => x.IsDeleted, false),
            Builders<Voucher>.Filter.Eq(x => x.Status, TypeStatus.Actived),
            Builders<Voucher>.Filter.In(x => x.VoucherType, new[] { TypeVoucher.Promotion, TypeVoucher.Transport }),
            string.IsNullOrEmpty(cartDto.ShopId) ? Builders<Voucher>.Filter.Empty : Builders<Voucher>.Filter.Eq(x => x.ShopId, cartDto.ShopId)
        );

        // Get vouchers
        var vouchers = await _collectionVoucher.Find(voucherFilter).ToListAsync();

        // Get all voucher details
        var allVoucherDetails = await _collectionVoucherDetail
            .Find(x => voucherDetailIds.Contains(x.VoucherDetailId) && !x.IsDeleted)
            .ToListAsync();

        // Xây dựng danh sách voucher hợp lệ cơ bản
        var validVouchers = new List<Voucher>();
        foreach (var voucherUser in allVoucherUsers)
        {
            var voucher = vouchers.FirstOrDefault(v => v.VoucherId == voucherUser.VoucherId);
            if (voucher == null) continue;

            // Kiểm tra điều kiện hợp lệ cơ bản
            var isValidVoucher = voucher.Status == TypeStatus.Actived &&
                                voucher.Quantity > voucher.QuantityUsed &&
                                (voucher.IsLongTerm || (voucher.EndDate.HasValue && DateTime.Now <= voucher.EndDate)) &&
                                (voucher.VoucherType == TypeVoucher.Promotion || voucher.VoucherType == TypeVoucher.Transport);

            if (!isValidVoucher) continue;

            // Kiểm tra điều kiện cart validation
            if (ValidateVoucher.IsVoucherDisabled(voucher, cartDto)) continue;

            // Tính toán RemainingStock cho voucher
            var distributedCount = await GetVoucherDistributedCount(voucher.VoucherId);
            voucher.RemainingStock = Math.Max(0, voucher.Quantity - distributedCount);

            if (voucher.CodeType == TypeVoucherCode.Common)
            {
                var detail = allVoucherDetails.FirstOrDefault(d => d.VoucherDetailId == voucherUser.VoucherDetailId);
                if (detail != null && voucherUser.NumUse > 0)
                {
                    detail.User = _userRepository.FindByUserId(voucherUser.UserId);
                    detail.NumUse = voucherUser.NumUse;
                    detail.BranchId = voucherUser.BranchId;
                    detail.CreatedDate = voucherUser.CreatedDate;
                    voucher.VoucherDetails = new List<VoucherDetail> { detail };
                    validVouchers.Add(voucher);
                }
            }
            else // Unique
            {
                var detail = allVoucherDetails.FirstOrDefault(d => d.VoucherDetailId == voucherUser.VoucherDetailId);
                if (detail != null && voucherUser.NumUse > 0)
                {
                    detail.User = _userRepository.FindByUserId(voucherUser.UserId);
                    detail.NumUse = voucherUser.NumUse;
                    detail.BranchId = voucherUser.BranchId;
                    detail.CreatedDate = voucherUser.CreatedDate;

                    var voucherClone = voucher.ShallowCopy();
                    voucherClone.RemainingStock = voucher.RemainingStock;
                    voucherClone.VoucherDetails = new List<VoucherDetail> { detail };
                    validVouchers.Add(voucherClone);
                }
            }
        }

        // Áp dụng phân trang cho danh sách hợp lệ
        var pagedValidVouchers = validVouchers
            .Skip(paging.PageIndex * paging.PageSize)
            .Take(paging.PageSize)
            .ToList();

        result.Result = pagedValidVouchers;
        result.Total = validVouchers.Count; // Tổng số voucher hợp lệ không phân trang
        return result;
    }

    public async Task<List<Voucher>> ListAllValidVouchersForCart(string userId, CartDto cartDto)
    {
        // Build base filter for voucher users
        var baseFilters = new List<FilterDefinition<VoucherUser>>
        {
            Builders<VoucherUser>.Filter.Eq(vu => vu.UserId, userId),
            Builders<VoucherUser>.Filter.Eq(vu => vu.IsDeleted, false)
        };

        var finalFilter = Builders<VoucherUser>.Filter.And(baseFilters);
        var query = _collectionVoucherUser.Find(finalFilter);

        // Get all voucher users (không phân trang)
        var sortDefinition = Builders<VoucherUser>.Sort.Descending(x => x.CreatedDate);
        var allVoucherUsers = await query.Sort(sortDefinition).ToListAsync();

        if (!allVoucherUsers.Any())
        {
            return new List<Voucher>();
        }

        // Get all voucherIds and detailIds
        var voucherIds = allVoucherUsers.Select(vu => vu.VoucherId).Distinct().ToList();
        var voucherDetailIds = allVoucherUsers.Select(vu => vu.VoucherDetailId).Distinct().ToList();

        // Build filter for vouchers - bao gồm cả Promotion và Transport
        var voucherFilter = Builders<Voucher>.Filter.And(
            Builders<Voucher>.Filter.In(x => x.VoucherId, voucherIds),
            Builders<Voucher>.Filter.Eq(x => x.IsDeleted, false),
            Builders<Voucher>.Filter.Eq(x => x.Status, TypeStatus.Actived),
            Builders<Voucher>.Filter.In(x => x.VoucherType, new[] { TypeVoucher.Promotion, TypeVoucher.Transport }),
            string.IsNullOrEmpty(cartDto.ShopId) ? Builders<Voucher>.Filter.Empty : Builders<Voucher>.Filter.Eq(x => x.ShopId, cartDto.ShopId)
        );

        // Get vouchers
        var vouchers = await _collectionVoucher.Find(voucherFilter).ToListAsync();

        // Get all voucher details
        var allVoucherDetails = await _collectionVoucherDetail
            .Find(x => voucherDetailIds.Contains(x.VoucherDetailId) && !x.IsDeleted)
            .ToListAsync();

        // Xây dựng danh sách voucher hợp lệ
        var validVouchers = new List<Voucher>();
        foreach (var voucherUser in allVoucherUsers)
        {
            var voucher = vouchers.FirstOrDefault(v => v.VoucherId == voucherUser.VoucherId);
            if (voucher == null) continue;

            // Kiểm tra điều kiện hợp lệ cơ bản
            var isValidVoucher = voucher.Status == TypeStatus.Actived &&
                                voucher.Quantity > voucher.QuantityUsed &&
                                (voucher.IsLongTerm || (voucher.EndDate.HasValue && DateTime.Now <= voucher.EndDate)) &&
                                (voucher.VoucherType == TypeVoucher.Promotion || voucher.VoucherType == TypeVoucher.Transport);

            if (!isValidVoucher) continue;

            // Kiểm tra điều kiện cart validation
            if (ValidateVoucher.IsVoucherDisabled(voucher, cartDto)) continue;

            // Tính toán RemainingStock cho voucher
            var distributedCount = await GetVoucherDistributedCount(voucher.VoucherId);
            voucher.RemainingStock = Math.Max(0, voucher.Quantity - distributedCount);

            if (voucher.CodeType == TypeVoucherCode.Common)
            {
                var detail = allVoucherDetails.FirstOrDefault(d => d.VoucherDetailId == voucherUser.VoucherDetailId);
                if (detail != null && voucherUser.NumUse > 0)
                {
                    detail.User = _userRepository.FindByUserId(voucherUser.UserId);
                    detail.NumUse = voucherUser.NumUse;
                    detail.BranchId = voucherUser.BranchId;
                    detail.CreatedDate = voucherUser.CreatedDate;
                    voucher.VoucherDetails = new List<VoucherDetail> { detail };
                    validVouchers.Add(voucher);
                }
            }
            else // Unique
            {
                var detail = allVoucherDetails.FirstOrDefault(d => d.VoucherDetailId == voucherUser.VoucherDetailId);
                if (detail != null && voucherUser.NumUse > 0)
                {
                    detail.User = _userRepository.FindByUserId(voucherUser.UserId);
                    detail.NumUse = voucherUser.NumUse;
                    detail.BranchId = voucherUser.BranchId;
                    detail.CreatedDate = voucherUser.CreatedDate;

                    var voucherClone = voucher.ShallowCopy();
                    voucherClone.RemainingStock = voucher.RemainingStock;
                    voucherClone.VoucherDetails = new List<VoucherDetail> { detail };
                    validVouchers.Add(voucherClone);
                }
            }
        }

        return validVouchers;
    }



    public async Task<Voucher?> UpdateVoucher(Voucher item)
    {
        var filter = Builders<Voucher>.Filter.Eq(x => x.VoucherId, item.VoucherId);
        var update = Builders<Voucher>.Update
            .Set(x => x.VoucherName, item.VoucherName)
            .Set(x => x.VoucherNameOrigin, item.VoucherName.NonUnicode().ToLower())
            .Set(x => x.VoucherType, item.VoucherType)
            .Set(x => x.Status, item.Status)
            .Set(x => x.ShopId, item.ShopId)
            .Set(x => x.Quantity, item.Quantity)
            .Set(x => x.QuantityUsed, item.QuantityUsed)
            .Set(x => x.IsLongTerm, item.IsLongTerm)
            .Set(x => x.StartDate, item.StartDate)
            .Set(x => x.EndDate, item.EndDate)
            .Set(x => x.DiscountType, item.DiscountType)
            .Set(x => x.PercentDiscount, item.PercentDiscount)
            .Set(x => x.MoneyDiscount, item.MoneyDiscount)
            .Set(x => x.MaxDiscount, item.MaxDiscount)
            .Set(x => x.LimitType, item.LimitType)
            .Set(x => x.CategoryIds, item.CategoryIds)
            .Set(x => x.ProductIds, item.ProductIds)
            .Set(x => x.MinOrder, item.MinOrder)
            .Set(x => x.ConditionType, item.ConditionType)
            .Set(x => x.UserGroupId, item.UserGroupId)
            .Set(x => x.UserIds, item.UserIds)
            .Set(x => x.MaxUsagePerUser, item.MaxUsagePerUser)
            .Set(x => x.RewardType, item.RewardType)
            .Set(x => x.IsFixedRewardPoint, item.IsFixedRewardPoint)
            .Set(x => x.RewardPoint, item.RewardPoint)
            .Set(x => x.RewardPointMin, item.RewardPointMin)
            .Set(x => x.RewardPointMax, item.RewardPointMax)
            .Set(x => x.RewardGiftIds, item.RewardGiftIds)
            .Set(x => x.CodeType, item.CodeType)
            .Set(x => x.IsBranchSpecific, item.IsBranchSpecific)
            .Set(x => x.ModifiedBy, item.ModifiedBy)
            .Set(x => x.ModifiedDate, item.ModifiedDate)
            .Set(x => x.Image, item.Image)
            .Set(x => x.ReleaseType, item.ReleaseType)
            .Set(x => x.ExchangePoints, item.ExchangePoints)
            .Set(x => x.VoucherCodePrefix, item.VoucherCodePrefix)
            .Set(x => x.ShippingDiscountType, item.ShippingDiscountType)
            .Set(x => x.DistributionType, item.DistributionType)
            .Set(x => x.PdfExport, item.PdfExport);

        var options = new FindOneAndUpdateOptions<Voucher> { ReturnDocument = ReturnDocument.After };
        return await _collectionVoucher.FindOneAndUpdateAsync(filter, update, options);
    }

    public async Task<long> TotalVoucherIds(string voucherIds)
    {
        var listIds = voucherIds.Split(',');
        FilterDefinition<Voucher> filterBuilders = Builders<Voucher>.Filter.And(
                Builders<Voucher>.Filter.In(x => x.VoucherId, listIds)
        );
        return await _collectionVoucher.CountDocumentsAsync(filterBuilders);
    }

    public async Task<long> TotalVoucher(string? shopId = null)
    {
        FilterDefinition<Voucher> filterBuilders = Builders<Voucher>.Filter.And(
            Builders<Voucher>.Filter.Where(p => shopId == null || p.ShopId == shopId)
        );
        return await _collectionVoucher.CountDocumentsAsync(filterBuilders);
    }

    public async Task<List<Voucher>> GetVouchersExpiringToday()
    {
        var today = DateTime.Today;
        var tomorrow = today.AddDays(1);
        var filter = Builders<Voucher>.Filter.And(
            Builders<Voucher>.Filter.Eq(x => x.IsLongTerm, false),
            Builders<Voucher>.Filter.Gte(x => x.EndDate, today),
            Builders<Voucher>.Filter.Lt(x => x.EndDate, tomorrow),
            Builders<Voucher>.Filter.Ne(x => x.Status, TypeStatus.Expired)
        );
        return await _collectionVoucher.Find(filter).ToListAsync();
    }

    #endregion Voucher ./

    //=== VoucherHistory
    #region VoucherHistory

    public async Task<VoucherHistory> CreateVoucherHistory(VoucherHistory history)
    {
        history.Id = Guid.NewGuid();
        history.VoucherHistoryId = Guid.NewGuid().ToString();
        await _collectionVoucherHistory.InsertOneAsync(history);
        return history;
    }

    public async Task<VoucherHistory> DeleteVoucherHistory(string id)
    {
        return await _collectionVoucherHistory.FindOneAndDeleteAsync(item => item.VoucherHistoryId == id);
    }

    public async Task<VoucherHistory?> FindByVoucherHistoryId(string id)
    {
        return await _collectionVoucherHistory.Find(item => item.VoucherHistoryId == id).FirstOrDefaultAsync();
    }

    public async Task<PagingResult<VoucherHistory>> ListVoucherHistory(Paging paging, string voucherId)
    {
        PagingResult<VoucherHistory> result = new PagingResult<VoucherHistory>();
        var filters = new List<FilterDefinition<VoucherHistory>>
        {
            Builders<VoucherHistory>.Filter.Eq(x => x.VoucherId, voucherId)
        };

        if (!string.IsNullOrEmpty(paging.Search))
        {
            filters.Add(Builders<VoucherHistory>.Filter.Regex(x => x.VoucherCode,
                new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "")}".EscapeSpecialChars(), "i")));
        }

        var filterBuilders = Builders<VoucherHistory>.Filter.And(filters);
        var query = _collectionVoucherHistory.Find(filterBuilders);

        result.Total = await query.CountDocumentsAsync();
        result.Result = await query
            .Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToListAsync();

        return result;
    }

    public async Task<VoucherHistory> UpdateVoucherHistory(VoucherHistory item)
    {
        var filter = Builders<VoucherHistory>.Filter.Eq(x => x.VoucherHistoryId, item.VoucherHistoryId);
        var options = new FindOneAndUpdateOptions<VoucherHistory> { ReturnDocument = ReturnDocument.After };
        return await _collectionVoucherHistory.FindOneAndUpdateAsync(filter, Builders<VoucherHistory>.Update.Set(x => x, item), options);
    }

    public async Task<List<VoucherHistory>> GetVoucherHistoryByUserId(string userId)
    {
        return await _collectionVoucherHistory.Find(x => x.UserId == userId).ToListAsync();
    }

    public async Task<List<VoucherHistory>> GetVoucherHistoryByVoucherDetailId(string voucherDetailId)
    {
        return await _collectionVoucherHistory.Find(x => x.VoucherDetailId == voucherDetailId).ToListAsync();
    }

    #endregion VoucherHistory ./

    //=== VoucherDetail
    #region VoucherDetail

    public async Task<VoucherDetail> CreateVoucherDetail(VoucherDetail item)
    {
        item.Id = Guid.NewGuid();
        item.VoucherDetailId = Guid.NewGuid().ToString();
        await _collectionVoucherDetail.InsertOneAsync(item);
        return item;
    }

    public async Task<bool> CreateVoucherDetails(List<VoucherDetail> items)
    {
        if (items == null || !items.Any())
            return false;

        // Generate IDs for all items
        foreach (var item in items)
        {
            item.Id = Guid.NewGuid();
            item.VoucherDetailId = Guid.NewGuid().ToString();
        }

        await _collectionVoucherDetail.InsertManyAsync(items);
        return true;
    }

    public async Task<VoucherDetail> DeleteVoucherDetail(string id)
    {
        var filter = Builders<VoucherDetail>.Filter.Eq(x => x.VoucherDetailId, id);
        var update = Builders<VoucherDetail>.Update
            .Set(x => x.IsDeleted, true);
        var options = new FindOneAndUpdateOptions<VoucherDetail> { ReturnDocument = ReturnDocument.After };
        return await _collectionVoucherDetail.FindOneAndUpdateAsync(filter, update, options);
    }

    public async Task<VoucherDetail?> FindByVoucherDetailId(string id)
    {
        var filter = Builders<VoucherDetail>.Filter.And(
            Builders<VoucherDetail>.Filter.Eq(x => x.VoucherDetailId, id),
            Builders<VoucherDetail>.Filter.Eq(x => x.IsDeleted, false)
        );
        return await _collectionVoucherDetail.Find(filter).FirstOrDefaultAsync();
    }

    public async Task<PagingResult<VoucherDetail>> ListVoucherDetailPaging(Paging paging, string voucherId)
    {
        PagingResult<VoucherDetail> result = new PagingResult<VoucherDetail>();
        var filters = new List<FilterDefinition<VoucherDetail>>
        {
            Builders<VoucherDetail>.Filter.Eq(x => x.VoucherId, voucherId),
            Builders<VoucherDetail>.Filter.Eq(x => x.IsDeleted, false)
        };
        if (!string.IsNullOrEmpty(paging.Search))
        {
            filters.Add(Builders<VoucherDetail>.Filter.Regex(x => x.VoucherCode,
                new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "").EscapeSpecialChars()}", "i")));
        }

        var filterBuilders = Builders<VoucherDetail>.Filter.And(filters);
        var query = _collectionVoucherDetail.Find(filterBuilders);

        result.Total = await query.CountDocumentsAsync();
        result.Result = await query
            .Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToListAsync();

        // Lấy thêm thông tin user cho từng detail nếu có
        foreach (var detail in result.Result)
        {
            var voucherUser = await _collectionVoucherUser
                .Find(x => x.VoucherDetailId == detail.VoucherDetailId && !x.IsDeleted)
                .FirstOrDefaultAsync();
            if (voucherUser != null)
            {
                detail.User = _userRepository.FindByUserId(voucherUser.UserId);
                detail.NumUse = voucherUser.NumUse;
                detail.BranchId = voucherUser.BranchId;
                detail.CreatedDate = voucherUser.CreatedDate;
            }
        }

        return result;
    }

    public async Task<VoucherDetail> UpdateVoucherDetail(VoucherDetail item)
    {
        var filter = Builders<VoucherDetail>.Filter.Eq(x => x.VoucherDetailId, item.VoucherDetailId);
        var result = await _collectionVoucherDetail.FindOneAndReplaceAsync(filter, item);
        return result;
    }

    public async Task<VoucherDetail?> GetVoucherDetailByCode(string code)
    {
        var filter = Builders<VoucherDetail>.Filter.And(
            Builders<VoucherDetail>.Filter.Eq(x => x.VoucherCode, code),
            Builders<VoucherDetail>.Filter.Eq(x => x.IsDeleted, false)
        );
        return await _collectionVoucherDetail.Find(filter).FirstOrDefaultAsync();
    }

    public async Task<VoucherDetail?> GetVoucherDetailByVoucherId(string voucherId)
    {
        var filter = Builders<VoucherDetail>.Filter.And(
            Builders<VoucherDetail>.Filter.Eq(x => x.VoucherId, voucherId),
            Builders<VoucherDetail>.Filter.Eq(x => x.IsDeleted, false)
        );
        return await _collectionVoucherDetail.Find(filter).FirstOrDefaultAsync();
    }

    public async Task<bool> DeleteVoucherDetailsByVoucherId(string voucherId)
    {
        var filter = Builders<VoucherDetail>.Filter.Eq(x => x.VoucherId, voucherId);
        var update = Builders<VoucherDetail>.Update
            .Set(x => x.IsDeleted, true);
        var result = await _collectionVoucherDetail.UpdateManyAsync(filter, update);
        return result.ModifiedCount > 0;
    }

    public async Task<bool> IsVoucherCodeExists(string code, string? excludeVoucherId = null)
    {
        var filter = Builders<VoucherDetail>.Filter.And(
            Builders<VoucherDetail>.Filter.Eq(x => x.VoucherCode, code),
            Builders<VoucherDetail>.Filter.Eq(x => x.IsDeleted, false)
        );
        if (!string.IsNullOrEmpty(excludeVoucherId))
        {
            filter &= Builders<VoucherDetail>.Filter.Ne(x => x.VoucherId, excludeVoucherId);
        }
        return await _collectionVoucherDetail.Find(filter).AnyAsync();
    }

    public async Task<List<VoucherDetail>> ListVoucherDetail(string voucherId)
    {
        var details = await _collectionVoucherDetail.Find(x => x.VoucherId == voucherId && !x.IsDeleted).ToListAsync();
        // foreach (var detail in details)
        // {
        //     var voucherUser = await _collectionVoucherUser
        //         .Find(x => x.VoucherDetailId == detail.VoucherDetailId && !x.IsDeleted)
        //         .FirstOrDefaultAsync();
        //     if (voucherUser != null)
        //     {
        //         detail.User = _userRepository.FindByUserId(voucherUser.UserId);
        //         detail.NumUse = voucherUser.NumUse;
        //         detail.BranchId = voucherUser.BranchId;
        //         detail.CreatedDate = voucherUser.CreatedDate;
        //     }
        // }
        return details;
    }

    public async Task<string?> GetVoucherCodeByDetailId(string voucherDetailId)
    {
        var detail = await _collectionVoucherDetail
            .Find(x => x.VoucherDetailId == voucherDetailId && !x.IsDeleted)
            .FirstOrDefaultAsync();
        return detail?.VoucherCode;
    }

    #endregion VoucherDetail ./

    //=== VoucherBranch
    #region VoucherBranch

    public async Task<VoucherBranch> CreateVoucherBranch(VoucherBranch branch)
    {
        branch.Id = Guid.NewGuid();
        await _collectionVoucherBranch.InsertOneAsync(branch);
        return branch;
    }

    public async Task<VoucherBranch> DeleteVoucherBranch(string id)
    {
        return await _collectionVoucherBranch.FindOneAndDeleteAsync(item => item.VoucherBranchId == id);
    }

    public async Task<VoucherBranch?> FindByVoucherBranchId(string id)
    {
        return await _collectionVoucherBranch.Find(item => item.VoucherBranchId == id).FirstOrDefaultAsync();
    }

    public async Task<PagingResult<VoucherBranch>> ListVoucherBranch(Paging paging, string voucherId)
    {
        PagingResult<VoucherBranch> result = new PagingResult<VoucherBranch>();
        var filters = new List<FilterDefinition<VoucherBranch>>
        {
            Builders<VoucherBranch>.Filter.Eq(x => x.VoucherId, voucherId)
        };

        if (!string.IsNullOrEmpty(paging.Search))
        {
            filters.Add(Builders<VoucherBranch>.Filter.Regex(x => x.BranchId,
                new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "")}".EscapeSpecialChars(), "i")));
        }

        var filterBuilders = Builders<VoucherBranch>.Filter.And(filters);
        var query = _collectionVoucherBranch.Find(filterBuilders);

        result.Total = await query.CountDocumentsAsync();
        result.Result = await query
            .Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToListAsync();

        return result;
    }

    public async Task<VoucherBranch> UpdateVoucherBranch(VoucherBranch item)
    {
        var filter = Builders<VoucherBranch>.Filter.Eq(x => x.VoucherBranchId, item.VoucherBranchId);
        var options = new FindOneAndUpdateOptions<VoucherBranch> { ReturnDocument = ReturnDocument.After };
        return await _collectionVoucherBranch.FindOneAndUpdateAsync(filter, Builders<VoucherBranch>.Update.Set(x => x, item), options);
    }

    public async Task<List<VoucherBranch>> GetVoucherBranchesByVoucherId(string voucherId)
    {
        return await _collectionVoucherBranch.Find(x => x.VoucherId == voucherId).ToListAsync();
    }

    public async Task<bool> DeleteVoucherBranchesByVoucherId(string voucherId)
    {
        var filter = Builders<VoucherBranch>.Filter.Eq(x => x.VoucherId, voucherId);
        var result = await _collectionVoucherBranch.DeleteManyAsync(filter);
        return result.DeletedCount > 0;
    }

    public async Task<VoucherBranch?> GetVoucherBranchByVoucherIdAndBranchId(string voucherId, string branchId)
    {
        return await _collectionVoucherBranch.Find(x => x.VoucherId == voucherId && x.BranchId == branchId).FirstOrDefaultAsync();
    }

    #endregion VoucherBranch ./

    //=== VoucherUser
    #region VoucherUser

    public async Task<VoucherUser> CreateVoucherUser(VoucherUser user)
    {
        user.Id = Guid.NewGuid();
        await _collectionVoucherUser.InsertOneAsync(user);
        return user;
    }

    public async Task<VoucherUser> DeleteVoucherUser(string id)
    {
        var filter = Builders<VoucherUser>.Filter.Eq(x => x.VoucherUserId, id);
        var update = Builders<VoucherUser>.Update
            .Set(x => x.IsDeleted, true);
        var options = new FindOneAndUpdateOptions<VoucherUser> { ReturnDocument = ReturnDocument.After };
        return await _collectionVoucherUser.FindOneAndUpdateAsync(filter, update, options);
    }

    public async Task<VoucherUser?> FindByVoucherUserId(string id)
    {
        return await _collectionVoucherUser.Find(item => item.VoucherUserId == id).FirstOrDefaultAsync();
    }

    public async Task<PagingResult<VoucherUser>> ListVoucherUser(Paging paging, string voucherId)
    {
        PagingResult<VoucherUser> result = new PagingResult<VoucherUser>();
        var filters = new List<FilterDefinition<VoucherUser>>
        {
            Builders<VoucherUser>.Filter.Eq(x => x.VoucherId, voucherId)
        };

        if (!string.IsNullOrEmpty(paging.Search))
        {
            filters.Add(Builders<VoucherUser>.Filter.Regex(x => x.UserId,
                new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "")}".EscapeSpecialChars(), "i")));
        }

        var filterBuilders = Builders<VoucherUser>.Filter.And(filters);
        var query = _collectionVoucherUser.Find(filterBuilders);

        result.Total = await query.CountDocumentsAsync();
        result.Result = await query
            .Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToListAsync();

        return result;
    }

    public async Task<VoucherUser> UpdateVoucherUser(VoucherUser item)
    {
        var filter = Builders<VoucherUser>.Filter.Eq(x => x.VoucherUserId, item.VoucherUserId);
        var update = Builders<VoucherUser>.Update
            .Set(x => x.NumUse, item.NumUse)
            .Set(x => x.ModifiedBy, item.ModifiedBy)
            .Set(x => x.ModifiedDate, item.ModifiedDate);

        var options = new FindOneAndUpdateOptions<VoucherUser> { ReturnDocument = ReturnDocument.After };
        return await _collectionVoucherUser.FindOneAndUpdateAsync(filter, update, options);
    }

    public async Task<VoucherUser?> GetVoucherUserByVoucherDetailIdAndUserId(string voucherDetailId, string userId)
    {
        var filter = Builders<VoucherUser>.Filter.And(
            Builders<VoucherUser>.Filter.Eq(x => x.VoucherDetailId, voucherDetailId),
            Builders<VoucherUser>.Filter.Eq(x => x.UserId, userId),
            Builders<VoucherUser>.Filter.Eq(x => x.IsDeleted, false)
        );
        return await _collectionVoucherUser.Find(filter).FirstOrDefaultAsync();
    }

    public async Task<VoucherUser?> GetVoucherUserByVoucherIdAndUserId(string voucherId, string userId)
    {
        var filter = Builders<VoucherUser>.Filter.And(
            Builders<VoucherUser>.Filter.Eq(x => x.VoucherId, voucherId),
            Builders<VoucherUser>.Filter.Eq(x => x.UserId, userId),
            Builders<VoucherUser>.Filter.Eq(x => x.IsDeleted, false)
        );
        return await _collectionVoucherUser.Find(filter).FirstOrDefaultAsync();
    }
    public async Task<VoucherUser?> GetVoucherUserByVoucherCodeAndUserId(string voucherCode, string userId)
    {
        var filter = Builders<VoucherUser>.Filter.And(
            Builders<VoucherUser>.Filter.Eq(x => x.VoucherCode, voucherCode),
            Builders<VoucherUser>.Filter.Eq(x => x.UserId, userId),
            Builders<VoucherUser>.Filter.Eq(x => x.IsDeleted, false)
        );
        return await _collectionVoucherUser.Find(filter).FirstOrDefaultAsync();
    }

    public async Task<List<VoucherUser>> GetVoucherUsersByUserId(string userId)
    {
        var filter = Builders<VoucherUser>.Filter.And(
            Builders<VoucherUser>.Filter.Eq(x => x.UserId, userId),
            Builders<VoucherUser>.Filter.Eq(x => x.IsDeleted, false)
        );
        return await _collectionVoucherUser.Find(filter).ToListAsync();
    }

    public async Task<bool> CreateVoucherUsers(List<VoucherUser> voucherUsers)
    {
        await _collectionVoucherUser.InsertManyAsync(voucherUsers);
        return true;
    }

    public async Task DeleteVoucherUsersByVoucherId(string voucherId)
    {
        var filter = Builders<VoucherUser>.Filter.Eq(x => x.VoucherId, voucherId);
        await _collectionVoucherUser.DeleteManyAsync(filter);
    }

    public async Task<List<VoucherUser>> GetVoucherUsersByVoucherCode(string voucherCode)
    {
        var filter = Builders<VoucherUser>.Filter.Eq(x => x.VoucherCode, voucherCode);
        return await _collectionVoucherUser.Find(filter).ToListAsync();
    }

    public async Task<PagingResult<Voucher>> ListSystemVouchers(Paging paging, string? userId, string shopId)
    {
        var result = new PagingResult<Voucher>();
        List<string> collectedVoucherIds = new List<string>();
        if (!string.IsNullOrEmpty(userId))
        {
            var userVouchers = await GetVoucherUsersByUserId(userId);
            collectedVoucherIds = userVouchers.Select(v => v.VoucherId).ToList();
        }

        // Build filter for system vouchers
        var filter = Builders<Voucher>.Filter.And(
            Builders<Voucher>.Filter.Eq(x => x.CodeType, TypeVoucherCode.Common),
            Builders<Voucher>.Filter.Eq(x => x.Status, TypeStatus.Actived),
            Builders<Voucher>.Filter.Eq(x => x.ShopId, shopId),
            Builders<Voucher>.Filter.Eq(x => x.IsDeleted, false),
            new BsonDocument("$expr", new BsonDocument("$gt", new BsonArray { "$Quantity", "$QuantityUsed" })),
            // Exclude vouchers with user group or user IDs restrictions
            Builders<Voucher>.Filter.Or(
                Builders<Voucher>.Filter.Eq(x => x.ConditionType, TypeCondition.All),
                Builders<Voucher>.Filter.And(
                    Builders<Voucher>.Filter.Eq(x => x.ConditionType, TypeCondition.Group),
                    Builders<Voucher>.Filter.Eq(x => x.UserGroupId, null)
                ),
                Builders<Voucher>.Filter.And(
                    Builders<Voucher>.Filter.Eq(x => x.ConditionType, TypeCondition.Customer),
                    Builders<Voucher>.Filter.Size(x => x.UserIds, 0)
                )
            )
        );

        // Exclude collected vouchers if any and userId is not null/empty
        if (!string.IsNullOrEmpty(userId) && collectedVoucherIds.Any())
        {
            filter = Builders<Voucher>.Filter.And(
                filter,
                Builders<Voucher>.Filter.Nin(x => x.VoucherId, collectedVoucherIds)
            );
        }

        // Add search filter if provided
        if (!string.IsNullOrEmpty(paging.Search))
        {
            // Search in voucher details by code
            var voucherDetailFilter = Builders<VoucherDetail>.Filter.And(
                Builders<VoucherDetail>.Filter.Regex(x => x.VoucherCode, new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "").EscapeSpecialChars()}", "i")),
                Builders<VoucherDetail>.Filter.Eq(x => x.IsDeleted, false)
            );
            var matchingVoucherIds = await _collectionVoucherDetail
                .Find(voucherDetailFilter)
                .Project(x => x.VoucherId)
                .ToListAsync();

            if (matchingVoucherIds.Any())
            {
                filter = Builders<Voucher>.Filter.And(
                    filter,
                    Builders<Voucher>.Filter.In(x => x.VoucherId, matchingVoucherIds)
                );
            }
            else
            {
                result.Total = 0;
                result.Result = new List<Voucher>();
                return result;
            }
        }

        // Get total count first
        result.Total = await _collectionVoucher.CountDocumentsAsync(filter);

        // Get paginated vouchers with proper sorting by CreatedDate descending
        var sortDefinition = Builders<Voucher>.Sort.Descending(x => x.CreatedDate);

        var vouchers = await _collectionVoucher
            .Find(filter)
            .Sort(sortDefinition)
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToListAsync();

        // Get voucher details and reward products for each voucher
        foreach (var voucher in vouchers)
        {
            if (voucher.CodeType == TypeVoucherCode.Common)
            {
                var voucherDetails = await _collectionVoucherDetail
                    .Find(x => x.VoucherId == voucher.VoucherId && !x.IsDeleted)
                    .ToListAsync();
                voucher.VoucherDetails = voucherDetails;
                voucher.VoucherDetailCount = voucherDetails.Count;
            }
            else // Unique
            {
                var detailCount = await _collectionVoucherDetail.CountDocumentsAsync(x => x.VoucherId == voucher.VoucherId && !x.IsDeleted);
                voucher.VoucherDetailCount = (int)detailCount;
                voucher.VoucherDetails = null;
            }

            if (voucher.RewardType == TypeReward.Product && voucher.RewardGiftIds != null && voucher.RewardGiftIds.Any())
            {
                var rewardProducts = await GetRewardProducts(voucher.RewardGiftIds);
                voucher.RewardProducts = rewardProducts;
            }

            // Tính toán RemainingStock cho system voucher
            var distributedCount = await GetVoucherDistributedCount(voucher.VoucherId);
            voucher.RemainingStock = Math.Max(0, voucher.Quantity - distributedCount);
        }

        result.Result = vouchers;
        return result;
    }

    #endregion VoucherUser ./

    public async Task<Voucher?> GetVoucherByUser(string voucherId, string userId)
    {
        var voucher = await _collectionVoucher.Find(x => x.VoucherId == voucherId && x.Status == TypeStatus.Actived).FirstOrDefaultAsync();
        if (voucher == null)
            return null;

        // Get voucher details with specific fields
        var voucherDetails = await _collectionVoucherDetail
            .Find(x => x.VoucherId == voucherId && !x.IsDeleted)
            .Project<VoucherDetail>(Builders<VoucherDetail>.Projection
                .Include(x => x.VoucherDetailId)
                .Include(x => x.VoucherId)
                .Include(x => x.VoucherCode)
                .Include(x => x.VoucherCodeLink)
                .Include(x => x.Status)
                .Include(x => x.IsSystemAssigned)
                .Include(x => x.CreatedBy)
                .Include(x => x.CreatedDate)
                .Include(x => x.ModifiedBy)
                .Include(x => x.ModifiedDate))
            .ToListAsync();

        foreach (var detail in voucherDetails)
        {
            var voucherUser = await _collectionVoucherUser
                .Find(x => x.VoucherDetailId == detail.VoucherDetailId && x.UserId == userId)
                .FirstOrDefaultAsync();

            if (voucherUser != null)
            {
                detail.User = _userRepository.FindByUserId(voucherUser.UserId);
                detail.NumUse = voucherUser.NumUse;
                detail.BranchId = voucherUser.BranchId;
            }
        }

        voucher.VoucherDetails = voucherDetails;
        return voucher;
    }

    public async Task<Voucher?> GetVoucherByCode(string voucherCode, string userId)
    {
        var voucherDetail = await _collectionVoucherDetail
            .Find(x => x.VoucherCode == voucherCode && !x.IsDeleted)
            .FirstOrDefaultAsync();

        if (voucherDetail == null)
            return null;

        var voucher = await _collectionVoucher
            .Find(x => x.VoucherId == voucherDetail.VoucherId && x.Status == TypeStatus.Actived)
            .FirstOrDefaultAsync();

        if (voucher == null)
            return null;

        // Chỉ lấy VoucherUser nếu cần thiết
        var voucherUser = await _collectionVoucherUser
            .Find(x => x.VoucherDetailId == voucherDetail.VoucherDetailId && x.UserId == userId)
            .FirstOrDefaultAsync();

        if (voucher.CodeType == TypeVoucherCode.Unique)
        {
            // Đối với voucher unique, kiểm tra quyền sở hữu
            if (voucherUser != null)
            {
                // User hiện tại sở hữu voucher này, hiển thị thông tin đầy đủ
                voucherDetail.User = _userRepository.FindByUserId(voucherUser.UserId);
                voucherDetail.NumUse = voucherUser.NumUse;
                voucherDetail.BranchId = voucherUser.BranchId;
            }
            else
            {
                // Kiểm tra xem voucher đã được ai khác thu thập chưa
                var anyVoucherUser = await _collectionVoucherUser
                    .Find(x => x.VoucherDetailId == voucherDetail.VoucherDetailId)
                    .FirstOrDefaultAsync();
                if (anyVoucherUser != null)
                {
                    // Voucher đã được user khác thu thập, không hiển thị
                    return null;
                }
                // Nếu chưa ai thu thập, hiển thị thông tin cơ bản
            }
        }
        else // Common voucher
        {
            // For common voucher, include user data only if user has collected it
            if (voucherUser != null)
            {
                voucherDetail.User = _userRepository.FindByUserId(voucherUser.UserId);
                voucherDetail.NumUse = voucherUser.NumUse;
                voucherDetail.BranchId = voucherUser.BranchId;
            }
            // If user hasn't collected it, return basic voucher info
        }

        voucher.VoucherDetails = new List<VoucherDetail> { voucherDetail };

        // Get reward products if RewardType is "product"
        if (voucher.RewardType == TypeReward.Product && voucher.RewardGiftIds != null && voucher.RewardGiftIds.Any())
        {
            var rewardProducts = await GetRewardProducts(voucher.RewardGiftIds);
            voucher.RewardProducts = rewardProducts;
        }

        return voucher;
    }

    public async Task<bool> IncrementVoucherQuantityUsed(string voucherId, int amount)
    {
        var filter = Builders<Voucher>.Filter.Eq(x => x.VoucherId, voucherId);
        var update = Builders<Voucher>.Update.Inc(x => x.QuantityUsed, amount);
        var result = await _collectionVoucher.UpdateOneAsync(filter, update);
        return result.ModifiedCount > 0;
    }

    public async Task<int> GetVoucherDistributedCount(string voucherId)
    {
        // Đếm số voucher đã được phát cho user (bao gồm cả chưa sử dụng)
        // Điều này khác với QuantityUsed - chỉ tính voucher đã sử dụng
        // VoucherUser track tất cả voucher đã phát cho user, dù đã dùng hay chưa
        var filter = Builders<VoucherUser>.Filter.And(
            Builders<VoucherUser>.Filter.Eq(x => x.VoucherId, voucherId),
            Builders<VoucherUser>.Filter.Eq(x => x.IsDeleted, false)
        );
        return (int)await _collectionVoucherUser.CountDocumentsAsync(filter);
    }
    public async Task<Voucher?> GetVoucherByCodePublic(string voucherCode)
    {
        var voucherDetail = await _collectionVoucherDetail
            .Find(x => x.VoucherCode == voucherCode && !x.IsDeleted)
            .FirstOrDefaultAsync();

        if (voucherDetail == null)
            return null;

        var voucher = await _collectionVoucher
            .Find(x => x.VoucherId == voucherDetail.VoucherId && x.Status == TypeStatus.Actived)
            .FirstOrDefaultAsync();

        if (voucher == null)
            return null;

        // Kiểm tra voucher unique đã được thu thập hay chưa
        if (voucher.CodeType == TypeVoucherCode.Unique)
        {
            var anyVoucherUser = await _collectionVoucherUser
                .Find(x => x.VoucherDetailId == voucherDetail.VoucherDetailId)
                .FirstOrDefaultAsync();
            if (anyVoucherUser != null)
            {
                // Voucher unique đã được thu thập, không hiển thị trong public API
                return null;
            }
        }

        voucher.VoucherDetails = new List<VoucherDetail> { voucherDetail };
        if (voucher.RewardType == TypeReward.Product && voucher.RewardGiftIds != null && voucher.RewardGiftIds.Any())
        {
            var rewardProducts = await GetRewardProducts(voucher.RewardGiftIds);
            voucher.RewardProducts = rewardProducts;
        }

        // Tính toán RemainingStock cho public API
        var distributedCount = await GetVoucherDistributedCount(voucher.VoucherId);
        voucher.RemainingStock = Math.Max(0, voucher.Quantity - distributedCount);

        return voucher;
    }
}