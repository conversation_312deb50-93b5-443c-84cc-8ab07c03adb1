#!/usr/bin/env node

/**
 * Script import dữ liệu customers và orders vào MongoDB
 * Usage: node nhanh-customer-order-mapper.js <customers.json> <orders.json> <shopId> <partnerId> [connection-string] [database-name]
 */

const fs = require("fs");
const { UUID } = require("mongodb");

function generateUUID() {
  return new UUID().toString();
}

// MongoDB connection (cần cài đặt: npm install mongodb)
let MongoClient;
try {
  MongoClient = require("mongodb").MongoClient;
} catch (error) {
  console.error("❌ Cần cài đặt MongoDB driver: npm install mongodb");
  process.exit(1);
}

const DEFAULT_CONNECTION =
  "*******************************************************************";
const DEFAULT_DATABASE = "evo-retail";
const USERS_COLLECTION = "User";
const ORDERS_COLLECTION = "Order";
const ITEMS_COLLECTION = "Items";

/**
 * Get all items by shop từ MongoDB
 */
async function getAllItemsByShop(db, shopId) {
  console.log(`📦 Đang lấy tất cả Items của shop ${shopId}...`);

  const itemsCollection = db.collection(ITEMS_COLLECTION);
  const items = await itemsCollection.find({ ShopId: shopId }).toArray();

  console.log(`✅ Đã lấy ${items.length} items từ database`);

  // Tạo map để lookup nhanh: ExternalId -> Item
  const itemsMap = new Map();
  items.forEach((item) => {
    if (item.ExternalId) {
      itemsMap.set(item.ExternalId, item);
    }
    // Cũng map theo ItemsCode để fallback
    if (item.ItemsCode) {
      itemsMap.set(item.ItemsCode, item);
    }
  });

  console.log(`📋 Đã tạo items map với ${itemsMap.size} entries`);
  return itemsMap;
}

/**
 * Map customer Nhanh sang User
 */
function mapNhanhCustomerToUser(nhanhCustomer, shopId, partnerId) {
  const now = new Date().toISOString();
  return {
    UserId: generateUUID(),
    ShopId: shopId,
    PartnerId: partnerId,
    Email: nhanhCustomer.email || "",
    PhoneNumber: nhanhCustomer.mobile || "",
    Fullname: nhanhCustomer.name || nhanhCustomer.fullName || "",
    Address: nhanhCustomer.address || "",
    Status: "Actived",
    Created: now,
    Updated: now,
    ExternalSource: "NhanhVN",
    ExternalId:
      nhanhCustomer.id?.toString() ||
      nhanhCustomer.customerId?.toString() ||
      "",
  };
}

/**
 * Map order Nhanh sang Order
 */
function mapNhanhOrderToOrder(
  nhanhOrder,
  shopId,
  partnerId,
  userIdMap,
  itemsMap
) {
  const now = new Date().toISOString();
  // Map trạng thái đơn hàng
  function mapOrderStatus(status) {
    if (!status) return "Pending";
    const s = status.trim().toLowerCase();
    if (
      [
        "new",
        "confirming",
        "customerconfirming",
        "waitingfordelivery",
        "waitingforpickup",
        "waitingforpayment",
        "waitingforstock",
        "waitingforconfirm",
        "waitingforreturn",
        "waitingforrefund",
      ].includes(s)
    )
      return "Pending";
    if (["confirmed"].includes(s)) return "Verified";
    if (["delivering"].includes(s)) return "Pending";
    if (["delivered", "success"].includes(s)) return "Success";
    if (["canceled"].includes(s)) return "Failed";
    if (["return", "refund"].includes(s)) return "Refund";
    if (["paid"].includes(s)) return "Paid";
    return "Pending";
  }
  // Map trạng thái vận chuyển
  function mapTransportStatus(status) {
    if (!status) return "Created";
    const s = status.trim().toLowerCase();
    if (
      [
        "new",
        "confirming",
        "customerconfirming",
        "waitingforpayment",
        "waitingforstock",
        "waitingforconfirm",
      ].includes(s)
    )
      return "Created";
    if (["confirmed"].includes(s)) return "Verified";
    if (["waitingfordelivery", "waitingforpickup"].includes(s))
      return "WaitingForDelivery";
    if (["delivering"].includes(s)) return "Delivering";
    if (["delivered"].includes(s)) return "Delivered";
    if (["success"].includes(s)) return "Success";
    if (["canceled"].includes(s)) return "Cancel";
    if (["return", "waitingforreturn"].includes(s)) return "Refunding";
    if (["refund", "waitingforrefund"].includes(s)) return "Refunded";
    return "Created";
  }

  // Map sản phẩm trong đơn hàng với dữ liệu Items từ database
  function mapOrderItems(order, itemsMap) {
    if (!order.products && !order.productList) return [];
    const products = order.products || order.productList;

    return products.map((prod) => {
      const productId = prod.id?.toString() || prod.productId?.toString();
      const productCode = prod.code;

      // Tìm item trong database theo ExternalId hoặc ItemsCode
      let dbItem = null;
      if (productId && itemsMap.has(productId)) {
        dbItem = itemsMap.get(productId);
      } else if (productCode && itemsMap.has(productCode)) {
        dbItem = itemsMap.get(productCode);
      }

      if (dbItem) {
        // Sử dụng dữ liệu từ database, chỉ override quantity và price từ order
        return {
          ItemsId: dbItem.ItemsId,
          ItemsCode: dbItem.ItemsCode,
          ItemsName: dbItem.ItemsName,
          ItemsNameOrigin: dbItem.ItemsNameOrigin,
          ItemsInfo: dbItem.ItemsInfo || "",
          Images: dbItem.Images || [],
          VariantImage: dbItem.VariantImage || { Type: "IMAGE", Link: "" },
          VariantNameOne: dbItem.VariantNameOne || "",
          VariantValueOne: dbItem.VariantValueOne || "",
          VariantNameTwo: dbItem.VariantNameTwo || "",
          VariantValueTwo: dbItem.VariantValueTwo || "",
          VariantNameThree: dbItem.VariantNameThree || "",
          VariantValueThree: dbItem.VariantValueThree || "",
          PriceCapital: dbItem.PriceCapital || 0,
          PriceReal: dbItem.PriceReal || prod.price || 0,
          Price: prod.price || dbItem.Price || 0, // Ưu tiên price từ order
          Quantity: prod.quantity || 1, // Quantity từ order
          IsVariant: dbItem.IsVariant || false,
          ItemsWeight: dbItem.ItemsWeight || 0,
          ItemsLength: dbItem.ItemsLength || 0,
          ItemsWidth: dbItem.ItemsWidth || 0,
          ItemsHeight: dbItem.ItemsHeight || 0,
          ExternalId: dbItem.ExternalId || productId || "",
          ExternalSource: dbItem.ExternalSource || "NhanhVN",
        };
      } else {
        // Fallback: tạo item cơ bản nếu không tìm thấy trong database
        console.warn(
          `⚠️  Không tìm thấy item trong DB: ${
            productId || productCode || "unknown"
          }`
        );
        return {
          ItemsId: generateUUID(),
          ItemsCode: productCode || "",
          ItemsName: prod.name || "",
          ItemsNameOrigin: (prod.name || "")
            .toLowerCase()
            .replace(/[^a-z0-9]/g, ""),
          ItemsInfo: "",
          Images: [],
          VariantImage: { Type: "IMAGE", Link: "" },
          VariantNameOne: "",
          VariantValueOne: "",
          VariantNameTwo: "",
          VariantValueTwo: "",
          VariantNameThree: "",
          VariantValueThree: "",
          PriceCapital: 0,
          PriceReal: prod.price || 0,
          Price: prod.price || 0,
          Quantity: prod.quantity || 1,
          IsVariant: false,
          ItemsWeight: 0,
          ItemsLength: 0,
          ItemsWidth: 0,
          ItemsHeight: 0,
          ExternalId: productId || "",
          ExternalSource: "NhanhVN",
        };
      }
    });
  }
  // Map UserId
  let userId = null;
  if (nhanhOrder.customerId && userIdMap[nhanhOrder.customerId]) {
    userId = userIdMap[nhanhOrder.customerId];
  }

  // Tạo UserShippingAddress object
  const userShippingAddress = {
    ShippingAddressId: null,
    UserId: userId,
    ProvinceId: null,
    ProvinceName: nhanhOrder.customerCity || nhanhOrder.customer_city || null,
    DistrictId: null,
    DistrictName:
      nhanhOrder.customerDistrict || nhanhOrder.customer_district || null,
    WardId: null,
    WardName: nhanhOrder.customerWard || nhanhOrder.customer_ward || null,
    Address: nhanhOrder.customerAddress || nhanhOrder.customer_address || "",
    FullName: nhanhOrder.customerName || nhanhOrder.customer_name || "",
    PhoneNumber: nhanhOrder.customerMobile || nhanhOrder.customer_mobile || "",
    IsDefault: false,
    Created: now,
    Updated: now,
  };

  return {
    OrderId: generateUUID(),
    OrderNo:
      nhanhOrder.orderNo ||
      nhanhOrder.order_no ||
      nhanhOrder.id?.toString() ||
      nhanhOrder.orderId?.toString() ||
      generateUUID(),
    TransportId: null,
    PaymentId: null,
    TransactionId: generateUUID(),
    PartnerId: partnerId,
    UserShippingAddress: userShippingAddress,
    Creator: userShippingAddress,
    ShopId: shopId,
    ShopName: "",
    ShopProvinceId: "",
    ShopProvinceName: "",
    ShopDistrictId: "",
    ShopDistrictName: "",
    ShopWardId: "",
    ShopWardName: "",
    ShopAddress: "",
    Notes: nhanhOrder.note || nhanhOrder.notes || null,
    OrderOrigin: "NhanhVN",
    ListItems: mapOrderItems(nhanhOrder, itemsMap),
    VoucherPromotionIds: [],
    VoucherPromotion: [],
    VoucherTransportIds: [],
    VoucherTransport: [],
    Price: nhanhOrder.calcTotalMoney || 0,
    ExchangePoints: 0,
    PointAfterCompleteOrder: 0,
    PointPrice: 0,
    VoucherPromotionPrice: 0,
    VoucherTransportPrice: 0,
    TransportPrice: nhanhOrder.shippingFee || 0,
    TransportService: nhanhOrder.shippingService || "LCOD",
    StatusTransport: mapTransportStatus(nhanhOrder.status),
    StatusDelivery: "InShop",
    StatusOrder: mapOrderStatus(nhanhOrder.status),
    TypePay: nhanhOrder.paymentMethod === "bank_transfer" ? "BANK" : "COD",
    StatusPay: nhanhOrder.paymentStatus === "paid" ? "Paid" : "NotPaid",
    StatusServe: null,
    Status: "Actived",
    BranchId: null,
    BranchDetail: null,
    TransportOrderId: null,
    Created: nhanhOrder.createdDateTime || nhanhOrder.created || now,
    Updated: now,
    PaymentInfo: null,
    CommissionBreakUp: null,
    CompletedAt: null,
    TransportOrderLabel: null,
    TaxInvoice: null,
    TaxSummary: [],
    TotalTaxAmount: 0,
    TotalAfterTax: nhanhOrder.calcTotalMoney || 0,
    VoucherCodes: [],
    ExternalSource: "NhanhVN",
    ExternalId:
      nhanhOrder.orderId?.toString() || nhanhOrder.id?.toString() || "",
  };
}

/**
 * Import data vào MongoDB với batch processing
 */
async function importToMongoDB(
  users,
  orders,
  connectionString,
  databaseName,
  shopId
) {
  const client = new MongoClient(connectionString);
  try {
    console.log("🔌 Đang kết nối MongoDB...");
    await client.connect();
    const db = client.db(databaseName);
    // Import users
    if (users.length > 0) {
      console.log(`👤 Import ${users.length} users...`);
      const usersCollection = db.collection(USERS_COLLECTION);
      try {
        const result = await usersCollection.insertMany(users, {
          ordered: false,
        });
        console.log(
          `✅ Users imported: ${result.insertedCount}/${users.length}`
        );
      } catch (error) {
        if (error.code === 11000) {
          const insertedCount = error.result?.insertedCount || 0;
          console.log(
            `⚠️  Users: ${insertedCount} imported, ${
              users.length - insertedCount
            } duplicates`
          );
        } else {
          console.error("❌ Users import failed:", error.message);
        }
      }
    }
    // Import orders
    if (orders.length > 0) {
      console.log(`📦 Import ${orders.length} orders...`);
      const ordersCollection = db.collection(ORDERS_COLLECTION);
      const batchSize = 100;
      let imported = 0;
      let errors = 0;
      for (let i = 0; i < orders.length; i += batchSize) {
        const batch = orders.slice(i, i + batchSize);
        try {
          const result = await ordersCollection.insertMany(batch, {
            ordered: false,
          });
          imported += result.insertedCount;
          console.log(
            `✅ Batch ${Math.floor(i / batchSize) + 1}: ${
              result.insertedCount
            }/${batch.length} orders`
          );
        } catch (error) {
          if (error.code === 11000) {
            const insertedCount = error.result?.insertedCount || 0;
            imported += insertedCount;
            errors += batch.length - insertedCount;
            console.log(
              `⚠️  Batch ${
                Math.floor(i / batchSize) + 1
              }: ${insertedCount} imported, ${
                batch.length - insertedCount
              } duplicates`
            );
          } else {
            console.error(
              `❌ Batch ${Math.floor(i / batchSize) + 1} failed:`,
              error.message
            );
            errors += batch.length;
          }
        }
      }
      console.log(
        `📊 Orders import summary: ${imported} success, ${errors} errors/duplicates`
      );
    }
  } catch (error) {
    console.error("❌ MongoDB connection error:", error.message);
    throw error;
  } finally {
    await client.close();
    console.log("🔌 MongoDB connection closed");
  }
}

/**
 * Main execution
 */
async function main() {
  const args = process.argv.slice(2);
  if (args.length < 4) {
    console.error(
      "❌ Usage: node nhanh-customer-order-mapper.js <customers.json> <orders.json> <shopId> <partnerId> [connection-string] [database-name]"
    );
    process.exit(1);
  }
  const [
    customersFile,
    ordersFile,
    shopId,
    partnerId,
    connectionString = DEFAULT_CONNECTION,
    databaseName = DEFAULT_DATABASE,
  ] = args;
  console.log("🚀 Nhanh.vn Customer & Order to MongoDB Mapper");
  console.log(`👤 Customers file: ${customersFile}`);
  console.log(`📦 Orders file: ${ordersFile}`);
  console.log(`🏪 Shop ID: ${shopId}`);
  console.log(`👤 Partner ID: ${partnerId}`);
  console.log(`🔌 MongoDB: ${connectionString}`);
  console.log(`🗄️  Database: ${databaseName}`);
  console.log("");
  try {
    // Đọc files
    console.log("📖 Đọc files...");
    if (!fs.existsSync(customersFile)) {
      throw new Error(`Customers file không tồn tại: ${customersFile}`);
    }
    if (!fs.existsSync(ordersFile)) {
      throw new Error(`Orders file không tồn tại: ${ordersFile}`);
    }
    const nhanhCustomers = JSON.parse(fs.readFileSync(customersFile, "utf8"));
    const nhanhOrders = JSON.parse(fs.readFileSync(ordersFile, "utf8"));
    if (!Array.isArray(nhanhCustomers)) {
      throw new Error("Customers file phải chứa array");
    }
    if (!Array.isArray(nhanhOrders)) {
      throw new Error("Orders file phải chứa array");
    }
    console.log(
      `📊 Loaded: ${nhanhCustomers.length} customers, ${nhanhOrders.length} orders`
    );
    // Kết nối MongoDB để lấy items
    console.log("🔌 Đang kết nối MongoDB để lấy items...");
    const client = new MongoClient(connectionString);
    await client.connect();
    const db = client.db(databaseName);

    // Lấy tất cả items của shop
    const itemsMap = await getAllItemsByShop(db, shopId);

    // Đóng connection tạm thời
    await client.close();

    // Map customers
    const users = nhanhCustomers.map((c) =>
      mapNhanhCustomerToUser(c, shopId, partnerId)
    );
    // Tạo map customerId -> UserId để map vào order
    const userIdMap = {};
    nhanhCustomers.forEach((c, idx) => {
      const cid = c.id || c.customerId;
      if (cid) userIdMap[cid] = users[idx].UserId;
    });
    // Map orders với items data từ database
    console.log("🔄 Đang map orders với items data...");
    const orders = nhanhOrders.map((o) =>
      mapNhanhOrderToOrder(o, shopId, partnerId, userIdMap, itemsMap)
    );
    // Import vào MongoDB
    await importToMongoDB(
      users,
      orders,
      connectionString,
      databaseName,
      shopId
    );
    console.log("\n🎉 Import hoàn thành!");
    console.log(`📊 Tổng kết:`);
    console.log(`   👤 Users: ${users.length}`);
    console.log(`   📦 Orders: ${orders.length}`);
  } catch (error) {
    console.error("❌ Lỗi:", error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
