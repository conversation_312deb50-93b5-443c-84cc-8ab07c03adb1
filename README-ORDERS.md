# Nhanh.vn Customer & Order Mapper

Script JavaScript để mapping và import dữ liệu customers và orders từ Nhanh.vn vào MongoDB với logic đúng như trong hệ thống C#.

## 🎯 **<PERSON><PERSON><PERSON> đích**

- Import customers từ Nhanh.vn thành Users trong hệ thống
- Import orders từ Nhanh.vn thành Orders trong hệ thống
- Tạo relationship giữa customers và orders
- Xử lý mapping status, địa chỉ, sản phẩm trong đơn hàng

## 📋 **Yêu cầu**

- Node.js (v14+)
- MongoDB driver: `npm install mongodb`
- **Đã import Items vào database trước** (sử dụng `nhanh-to-mongo-mapper.js`)
- 2 file JSON từ Nhanh.vn:
  - `customers.json`: Danh sách khách hàng
  - `orders.json`: Danh sách đơn hàng

## 🔄 **<PERSON><PERSON>ch sử dụng**

### C<PERSON> pháp:

```bash
node nhanh-customer-order-mapper.js <customers.json> <orders.json> <shopId> <partnerId> [connection-string] [database-name]
```

### Ví dụ:

```bash
# Với MongoDB local
node nhanh-customer-order-mapper.js customers.json orders.json "shop123" "partner456" "mongodb://localhost:27017" "ECommerce"

# Với MongoDB Atlas
node nhanh-customer-order-mapper.js customers.json orders.json "shop123" "partner456" "mongodb+srv://user:<EMAIL>" "ECommerce"

# Sử dụng connection mặc định
node nhanh-customer-order-mapper.js customers.json orders.json "shop123" "partner456"
```

## 📊 **Mapping Logic**

### **Customers → Users**

| Nhanh.vn Field      | System Field  | Logic                               |
| ------------------- | ------------- | ----------------------------------- |
| `id` / `customerId` | `ExternalId`  | Ưu tiên `id`, fallback `customerId` |
| `name` / `fullName` | `Fullname`    | Ưu tiên `name`, fallback `fullName` |
| `email`             | `Email`       | Direct mapping                      |
| `mobile`            | `PhoneNumber` | Direct mapping                      |
| `address`           | `Address`     | Direct mapping                      |

### **Orders → Orders**

| Nhanh.vn Field         | System Field                      | Logic                                  |
| ---------------------- | --------------------------------- | -------------------------------------- |
| `orderId` / `id`       | `ExternalId`                      | Ưu tiên `orderId`, fallback `id`       |
| `orderNo` / `order_no` | `OrderNo`                         | Ưu tiên `orderNo`, fallback `order_no` |
| `totalPrice` / `price` | `Price`                           | Ưu tiên `totalPrice`, fallback `price` |
| `customerId`           | `UserId`                          | Map qua customer relationship          |
| `status`               | `StatusOrder` + `StatusTransport` | Status mapping logic                   |

### **Status Mapping**

#### Order Status:

- `new`, `confirming`, `customerconfirming`, etc. → `"Pending"`
- `delivered`, `success` → `"Completed"`
- `cancelled`, `returned` → `"Cancelled"`

#### Transport Status:

- `new`, `confirming` → `"Pending"`
- `waitingfordelivery`, `delivering` → `"Shipping"`
- `delivered`, `success` → `"Delivered"`
- `cancelled`, `returned` → `"Cancelled"`

### **Shipping Address Mapping**

Hỗ trợ cả 2 format field names:

- `customerName` / `customer_name` → `FullName`
- `customerMobile` / `customer_mobile` → `PhoneNumber`
- `customerAddress` / `customer_address` → `Address`
- `customerCity` / `customer_city` → `ProvinceName`
- `customerDistrict` / `customer_district` → `DistrictName`
- `customerWard` / `customer_ward` → `WardName`

### **Order Items Mapping (Cải tiến)**

**🔥 Tính năng mới**: Lấy dữ liệu Items đầy đủ từ database thay vì chỉ map cơ bản

1. **Get all Items by shop** từ MongoDB trước khi process orders
2. **Lookup Items** theo `ExternalId` hoặc `ItemsCode`
3. **Sử dụng dữ liệu Items đầy đủ** từ database:
   - ItemsId, ItemsCode, ItemsName, ItemsNameOrigin
   - Images, VariantImage, Variant fields
   - PriceCapital, PriceReal, IsVariant
   - ItemsWeight, ItemsLength, ItemsWidth, ItemsHeight
4. **Override** chỉ `Quantity` và `Price` từ order data
5. **Fallback** tạo item cơ bản nếu không tìm thấy trong database

**Lợi ích:**

- ✅ Order items có đầy đủ thông tin như trong database
- ✅ Variant information được giữ nguyên
- ✅ Images và metadata được preserve
- ✅ Chỉ quantity và price được update từ order

## 🔧 **Test Script**

```bash
# Chạy test với sample data
node test-nhanh-order-mapper.js
```

## 📁 **Format JSON mong đợi**

### **customers.json**

```json
[
  {
    "id": 12345,
    "name": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "mobile": "0901234567",
    "address": "123 Đường ABC, Quận 1, TP.HCM"
  }
]
```

### **orders.json**

```json
[
  {
    "orderId": 67890,
    "orderNo": "ORD001",
    "customerId": 12345,
    "status": "new",
    "totalPrice": 500000,
    "customerName": "Nguyễn Văn A",
    "customerMobile": "0901234567",
    "customerAddress": "123 Đường ABC",
    "products": [
      {
        "id": 58739,
        "name": "Sản phẩm A",
        "quantity": 2,
        "price": 250000
      }
    ]
  }
]
```

## ⚡ **Performance**

- **Customers**: Import trực tiếp, xử lý duplicates
- **Orders**: Batch processing (100 orders/batch)
- **Memory**: Tối ưu với streaming JSON parsing
- **Speed**: ~1000 orders/phút

## 🔍 **Troubleshooting**

### **Lỗi thường gặp:**

1. **"Customers file phải chứa array"**

   - Đảm bảo file JSON là array `[...]`

2. **"MongoDB connection error"**

   - Kiểm tra connection string
   - Đảm bảo MongoDB đang chạy

3. **"Duplicate key error"**

   - Bình thường, có nghĩa là data đã tồn tại
   - Script sẽ skip và tiếp tục

4. **Orders không có UserId**
   - Kiểm tra `customerId` trong orders có match với customers không
   - Đảm bảo customers được import trước orders

### **Validation checklist:**

- ✅ File customers.json có đúng format array?
- ✅ File orders.json có đúng format array?
- ✅ Tất cả customers có `id` hoặc `customerId`?
- ✅ Tất cả orders có `customerId` để link?
- ✅ MongoDB connection string đúng?
- ✅ ShopId và PartnerId đúng?

## 📊 **Kết quả mong đợi**

```
🚀 Nhanh.vn Customer & Order to MongoDB Mapper
👤 Customers file: customers.json
📦 Orders file: orders.json
🏪 Shop ID: shop123
👤 Partner ID: partner456

📖 Đọc files...
📊 Loaded: 1500 customers, 3200 orders

👤 Import 1500 users...
✅ Users imported: 1500/1500

📦 Import 3200 orders...
✅ Batch 1: 100/100 orders
✅ Batch 2: 100/100 orders
...
📊 Orders import summary: 3200 success, 0 errors/duplicates

🎉 Import hoàn thành!
📊 Tổng kết:
   👤 Users: 1500
   📦 Orders: 3200
```

## 🎉 **Kết luận**

Script này giúp bạn:

- ✅ Import nhanh customers và orders từ Nhanh.vn
- ✅ Mapping đúng logic như hệ thống C#
- ✅ Xử lý relationship và status mapping
- ✅ Tối ưu performance với batch processing
- ✅ Xử lý errors và duplicates gracefully

Sẵn sàng để import data thực tế! 🚀
